package org.jeecg.modules.wechat.service.impl;

import org.jeecg.modules.wechat.dto.config.DeployConfigDTO;
import org.jeecg.modules.wechat.entity.SysDeployConfig;
import org.jeecg.modules.wechat.mapper.SysDeployConfigMapper;
import org.jeecg.modules.wechat.service.ISysDeployConfigService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 配置类型表
 * @Author: jeecg-boot
 * @Date:   2025-04-22
 * @Version: V1.0
 */

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 配置类型表
 * @Author: jeecg-boot
 * @Date:   2025-04-22
 * @Version: V1.0
 */
@Slf4j
@Service
public class SysDeployConfigServiceImpl extends ServiceImpl<SysDeployConfigMapper, SysDeployConfig> implements ISysDeployConfigService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * Redis缓存键前缀
     */
    private static final String REDIS_KEY_PREFIX = "integrated_service_ids:type:";


    /**
     * Redis缓存过期时间（小时）
     */
    private static final long REDIS_EXPIRE_HOURS = 24;

    /**
     * 系统配置类型常量，使用接口中定义的常量
     */
    private static final Integer DEPLOY_TYPE = 1;
    private static final Integer DEPLOY_TYPE_CAR_INSURANCE = 2;
    private static final Integer DEPLOY_TYPE_PROPERTY_INSURANCE = 3;
    private static final Integer DEPLOY_TYPE_VALUE_ADDED_SERVICE = 4;

    /**
     * 根据类型1获取配置信息，将json转化为实体类
     * 从Redis中获取，如果Redis中不存在则从数据库获取并缓存到Redis
     * @return DeployConfigDTO 配置实体类
     */
    @Override
    public DeployConfigDTO getDeployConfig() {
        return getDeployConfigByType(DEPLOY_TYPE, DeployConfigDTO.class);
    }

    /**
     * 根据指定类型获取配置信息，将json转化为指定的实体类
     * 从Redis中获取，如果Redis中不存在则从数据库获取并缓存到Redis
     * @param deployType 配置类型 (1-系统配置, 2-车险链接配置, 3-财险, 4-增值服务)
     * @param clazz 要转换成的实体类Class
     * @param <T> 实体类泛型
     * @return 配置实体类
     */
    @Override
    public <T> T getDeployConfigByType(Integer deployType, Class<T> clazz) {
        if (deployType == null || clazz == null) {
            return null;
        }

        String redisKey = REDIS_KEY_PREFIX + deployType;

        // 尝试从Redis获取配置
        Object cacheConfig = redisTemplate.opsForValue().get(redisKey);
        if (cacheConfig != null ) {
            try {
                // 检查缓存对象是否与请求的类型匹配
                if (clazz.isInstance(cacheConfig)) {
                    return clazz.cast(cacheConfig);
                } else {
                    // 类型不匹配，需要重新从数据库获取并转换
                    log.info("Redis缓存中的对象类型与请求的类型不匹配，重新从数据库获取");
                }
            } catch (Exception e) {
                log.error("Redis缓存中的对象转换失败", e);
            }
        }

        // Redis中不存在或类型不匹配，从数据库获取
        LambdaQueryWrapper<SysDeployConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDeployConfig::getDeployType, deployType);
        SysDeployConfig config = this.getOne(queryWrapper);

        T result = null;

        // 如果数据库中存在配置，则解析JSON
        if (config != null && StringUtils.isNotBlank(config.getDeployJson())) {
            try {
                result = JSON.parseObject(config.getDeployJson(), clazz);
                log.info("从数据库获取类型{}的配置成功", deployType);
            } catch (Exception e) {
                log.error("解析类型{}的配置JSON失败", deployType, e);
                // 解析失败时尝试创建一个空对象
                try {
                    result = clazz.newInstance();
                } catch (Exception ex) {
                    log.error("创建类型{}的默认配置对象失败", deployType, ex);
                    return null;
                }
            }
        } else {
            // 数据库中不存在配置，创建一个空对象
            log.info("数据库中不存在类型{}的配置，创建空对象", deployType);
            try {
                result = clazz.newInstance();
            } catch (Exception e) {
                log.error("创建类型{}的默认配置对象失败", deployType, e);
                return null;
            }
        }

        // 将配置缓存到Redis
        if (result != null) {
            redisTemplate.opsForValue().set(redisKey, result, REDIS_EXPIRE_HOURS, TimeUnit.HOURS);
        }

        return result;
    }

    /**
     * 保存或更新配置
     * @param deployConfig 配置实体类
     * @return 是否成功
     */
    @Override
    public boolean saveOrUpdateDeployConfig(DeployConfigDTO deployConfig) {
        if (deployConfig == null) {
            return false;
        }

        return saveOrUpdateConfigByType(DEPLOY_TYPE, deployConfig);
    }

    /**
     * 保存或更新指定类型的配置
     * @param deployType 配置类型 (1-系统配置, 2-车险链接配置, 3-财险, 4-增值服务)
     * @param config 配置实体类
     * @param <T> 实体类泛型
     * @return 是否成功
     */
    @Override
    public <T> boolean saveOrUpdateConfigByType(Integer deployType, T config) {
        if (deployType == null || config == null) {
            return false;
        }

        // 将实体类转换为JSON
        String jsonConfig = JSON.toJSONString(config);

        // 查询是否已存在配置
        LambdaQueryWrapper<SysDeployConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDeployConfig::getDeployType, deployType);
        SysDeployConfig existConfig = this.getOne(queryWrapper);

        boolean result;
        if (existConfig != null) {
            // 更新现有配置
            existConfig.setDeployJson(jsonConfig);
            result = this.updateById(existConfig);
        } else {
            // 创建新配置
            SysDeployConfig newConfig = new SysDeployConfig();
            newConfig.setDeployType(deployType);
            newConfig.setDeployJson(jsonConfig);
            result = this.save(newConfig);
        }

        if (result) {
            // 更新Redis缓存
            String redisKey = REDIS_KEY_PREFIX + deployType;
            redisTemplate.opsForValue().set(redisKey, config, REDIS_EXPIRE_HOURS, TimeUnit.HOURS);
        }

        return result;
    }

    /**
     * 清除配置缓存
     */
    @Override
    public void clearConfigCache(Integer deployType) {
        String redisKey = REDIS_KEY_PREFIX + deployType;
        redisTemplate.delete(redisKey);
        log.info("系统配置缓存已清除");
    }

    /**
     * 清除指定类型的配置缓存
     * @param deployType 配置类型 (1-系统配置, 2-车险链接配置, 3-财险, 4-增值服务)
     */
    @Override
    public void clearConfigCacheByType(Integer deployType) {
        if (deployType == null) {
            return;
        }
        String redisKey = REDIS_KEY_PREFIX + deployType;
        redisTemplate.delete(redisKey);
        log.info("类型{}的配置缓存已清除", deployType);
    }

    /**
     * 获取默认配置
     * @return 默认配置实体类
     */
    private DeployConfigDTO getDefaultConfig() {
        DeployConfigDTO defaultConfig = new DeployConfigDTO();
        // 姓名切换为先生/女士的开关
        defaultConfig.setNameSwitch(false);
        // 车牌号码脱敏开关
        defaultConfig.setPlateNoMask(false);
        // 手机号脱敏开关
        defaultConfig.setPhoneSwitch(false);
        // 车架号脱敏开关
        defaultConfig.setVinNoMask(false);
        // 数据实现方式（数据库存储或查询实现脱敏）
        defaultConfig.setQueryType("db");
        // 去除查询脱敏
        defaultConfig.setRemoveQueryMask(false);
        // 客服IP来源
        defaultConfig.setServiceIpType("server");
        // 服务器IP
        defaultConfig.setServerIp("");
        // 车险聊天源总条数
        defaultConfig.setCarInsuranceCount(0);
        // 财险聊天源总条数
        defaultConfig.setPropertyInsuranceCount(0);
        // 增值服务聊天源总条数
        defaultConfig.setValueAddedServiceCount(0);
        // PV/UV比值最小值
        defaultConfig.setPvUvRatioMin(new BigDecimal("1.2"));
        // PV/UV比值最大值
        defaultConfig.setPvUvRatioMax(new BigDecimal("3.0"));
        return defaultConfig;
    }
}