package org.jeecg.modules.wechat.dto.config;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 系统部署配置实体类
 * 对应前端配置项
 */
@Data
public class DeployConfigDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 姓名切换为先生/女士
     */
    private Boolean nameSwitch;

    /**
     * 车牌号码脱敏
     */
    private Boolean plateNoMask;

    /**
     * 手机号脱敏
     */
    private Boolean phoneSwitch;

    /**
     * 车架号脱敏
     */
    private Boolean vinNoMask;

    /**
     * 数据实现方式: db-数据库存储, query-查询实现脱敏
     */
    private String queryType;

    /**
     * 去除查询脱敏
     */
    private Boolean removeQueryMask;

    /**
     * 客服IP来源: server-服务器IP, tenant-租户设置中IP
     */
    private String serviceIpType;

    /**
     * 服务器IP
     */
    private String serverIp;

    /**
     * 车险聊天源总条数
     */
    private Integer carInsuranceCount;

    /**
     * 财险聊天源总条数
     */
    private Integer propertyInsuranceCount;

    @ApiModelProperty(value = "最低点击率")
    private BigDecimal clickRate;

    /**
     * 增值服务聊天源总条数
     */
    private Integer valueAddedServiceCount;

    @ApiModelProperty(value = "新闻点赞数最小值")
    private Integer newsLikeCountMin;
    @ApiModelProperty(value = "新闻点赞数最大值")
    private Integer newsLikeCountMax;

    /**
     * PV/UV比值最小值
     */
    @ApiModelProperty(value = "PV/UV比值最小值")
    private BigDecimal pvUvRatioMin;

    /**
     * PV/UV比值最大值
     */
    @ApiModelProperty(value = "PV/UV比值最大值")
    private BigDecimal pvUvRatioMax;
}